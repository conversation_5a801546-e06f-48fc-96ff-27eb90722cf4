// @ts-nocheck
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';

import { EHS_ROLE_URL, EPTW_ROLE_URL, EXTERNAL_USERS_URL, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, GROUP_EHS_ROLE_URL, REPORT_ROLE_URL, LOCATION1_URL, PLANT_ROLE_URL, USERS_URL, USERS_URL_WITH_ID, GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, INDIVIDUAL_USER_LOCATION_ROLE_URL, USER_LOCATION_ROLE_WITH_ID_URL, GET_MY_USER_LOCATION_ROLE_URL, LOCATION2_URL, LOCATION3_URL, LOCATION4_URL } from '../constants';
import API from '../services/API';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { userColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import { useSelector } from 'react-redux'
import MasterUserFilterLocation from './MasterUserFilterLocation';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const MasterUser = () => {
    const defaultMaterialTheme = createTheme();

    const [mdShow, setMdShow] = useState(false);
    const [userShow, setUserShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const uName = useRef();
    const uEmail = useRef();
    const uPassword = useRef();
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
    const [allLocationRoles, setAllLocationRoles] = useState([])
    const [country, setCountry] = useState([])
    const [locationTwo, setLocationTwo] = useState([])
    const [locationThree, setLocationThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    const [previewRoles, setPreviewRoles] = useState([])

    // Module structure for the new UI
    const moduleStructure = useMemo(() => [
        { name: 'EHS Observation', key: 'ehs', roles: allRoles.ehs },
        { name: 'ePermit to Work', key: 'eptw', roles: allRoles.eptw },
        { name: 'Incident Reporting', key: 'incident', roles: allRoles.incident },
        { name: 'Inspection and Audit', key: 'inspection', roles: allRoles.inspection },
        { name: 'Plant and Equipment', key: 'plant', roles: allRoles.plant },
        { name: 'EHS Statistics', key: 'report', roles: allRoles.report }
    ], [allRoles]);
    useEffect(() => {
        getCountry();
        getLocationTwo();
        getLocationThree();
        getLocationFour();
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getPlantRole();
        getGroupEhsRole();
        getReportRole();


    }, [])
    const getUserLocationRole = async (id) => {
        const response = await API.get(GET_MY_USER_LOCATION_ROLE_URL(id))

        if (response.status === 200) {

            setAllLocationRoles(response.data)
        }
    }


    const getCountry = async () => {
        const response = await API.get(LOCATION1_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, country: response.data } })
            setCountry(response.data)
        }
    }

    const getLocationTwo = async () => {
        const response = await API.get(LOCATION2_URL)

        if (response.status === 200) {

            setLocationTwo(response.data)
        }
    }

    const getLocationThree = async () => {
        const response = await API.get(LOCATION3_URL)

        if (response.status === 200) {

            setLocationThree(response.data)
        }
    }

    const getLocationFour = async () => {
        const response = await API.get(LOCATION4_URL)

        if (response.status === 200) {

            setLocationFour(response.data)
        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, inspection: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, plant: response.data } })
        }
    }

    const getGroupEhsRole = async () => {
        const response = await API.get(GROUP_EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, groupEhs: response.data } })
        }
    }

    const getReportRole = async () => {
        const response = await API.get(REPORT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, report: response.data } })
        }
    }


    const me = useSelector(state => state.login.user);
    const roleCountryMap = {
        "India (IN)": "India",
        "UK (UK)": "UK",
        "Singapore (SG)": "Singapore",
        "Thailand (TH)": "Thailand",
        "Korea (KR)": "Korea",
        "Philippines (PH)": "Philippines"
    };
    const roles = me.validationRoles;

    const allowedCountries = roles
        .map(role => roleCountryMap[role.name])
        .filter(country => country !== undefined);

    console.log(allowedCountries, 'allowed')

    const [data, setData] = useState([])

    const getUsersData = useCallback(async () => {
        const response = await API.get(USERS_URL);
        if (response.status === 200) {

            setData(response.data.filter(i => i.status !== false).filter(i => allowedCountries.includes(i.country)).sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())));

        }
    }, [allowedCountries]);

    useEffect(() => {
        getUsersData();
    }, [getUsersData])

    const viewAssignPermission = async (id, email, name) => {
        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            getUserLocationRole(id)
            setMdShow(true)

        }
    }



    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)
        setIndividualSelectedRole((prevRoles) => {
            if (e.target.checked) {
                // Add the role to the selected roles
                return { ...prevRoles, roles: [...prevRoles.roles, roleId] };
            } else {
                // Remove the role from the selected roles
                return { ...prevRoles, roles: prevRoles.roles.filter((id) => id !== roleId) };
            }
        });
        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category] || [];

            console.log(category, prevRoles, categoryRoles, 'check')
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        let flag = false;
        const response = await API.post(INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: id, roles: individualSelectedRole.roles, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } })
        if (response.status === 200) {
            flag = true;
        }
        const response2 = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response2.status === 204 && flag) {
            // setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [] })
            // setSelectedUserId({ id: "", email: "", name: "" })
            // setMdShow(false)
            // setIndividualSelectedRole({ roles: [] })
            cogoToast.info('Assigned', { position: 'top-right' })

        }

    }

    const handleAssignClose = () => {
        setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })
        setSelectedUserId({ id: "", email: "", name: "" })
        setMdShow(false)
        setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        setPreviewRoles([])
        setSelectedLocationOne('')
        setSelectedLocationTwo('')
        setSelectedLocationThree('')
        setSelectedLocationFour('')
    }
    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)

        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: uName.current.value,
            email: uEmail.current.value,
            password: uPassword.current.value,


        })
        if (response.status === 200) {

            cogoToast.info('Created!', { position: 'top-right' })
            $('#dataTable').DataTable().ajax.reload();
            customSwal2.fire(
                'User Created!',
                '',
                'success'
            )
        } else {
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }



        uName.current.value = '';
        uEmail.current.value = '';
        uPassword.current.value = '';
        setUserShow(false)
        setIsLoading(false)
    }

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        {
            icon: 'grading',
            tooltip: 'Role Assignment',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
            }
        }
    ]

    const localization = {
        header: {
            actions: 'Role Assignment'
        }
    };

    const [selectedLocationOne, setSelectedLocationOne] = useState('');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

        setSelectedLocationOne(locationOneId)
        setSelectedLocationTwo(locationTwoId)
        setSelectedLocationThree(locationThreeId)
        setSelectedLocationFour(locationFourId)
    };

    const [individualSelectedRole, setIndividualSelectedRole] = useState({ roles: [], disabledRoles: [] })

    const getIndividualRoles = useCallback(async () => {
        const response = await API.post(GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: selectedUserId.id, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } });
        if (response.status === 200) {
            if (response.data && response.data.length > 0)
                setIndividualSelectedRole(response.data[0])
            else
                setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        }
    }, [selectedUserId.id, selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour]);

    useEffect(() => {
        if (selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour)
            getIndividualRoles()
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, getIndividualRoles])

    const updatePreview = useCallback(() => {
        if (!selectedLocationOne && !selectedLocationTwo && !selectedLocationThree && !selectedLocationFour) {
            setPreviewRoles([]);
            return;
        }

        // Build location path
        let locationNames = [];
        const getLocationName = (id, locationArray, allText) => {
            if (id === "") return "";
            if (id && id.endsWith("-all")) return allText;
            const location = locationArray.find(location => location.id === id);
            return location ? location.name : 'Unknown';
        };

        locationNames.push(getLocationName(selectedLocationOne, country, 'All Country'));
        locationNames.push(getLocationName(selectedLocationTwo, locationTwo, 'All City'));
        locationNames.push(getLocationName(selectedLocationThree, locationThree, 'All Business Unit'));
        locationNames.push(getLocationName(selectedLocationFour, locationFour, 'All Projects / DC'));
        locationNames = locationNames.filter(name => name && name !== 'Unknown');

        // Group roles by module
        const rolesByModule = {};
        individualSelectedRole.roles.forEach(roleId => {
            moduleStructure.forEach(module => {
                const role = module.roles.find(r => r.id === roleId);
                if (role) {
                    if (!rolesByModule[module.name]) {
                        rolesByModule[module.name] = [];
                    }
                    rolesByModule[module.name].push(role.name);
                }
            });
        });

        if (Object.keys(rolesByModule).length > 0) {
            setPreviewRoles([{
                locationPath: locationNames.join(' > '),
                rolesByModule: rolesByModule
            }]);
        } else {
            setPreviewRoles([]);
        }
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, individualSelectedRole, moduleStructure, country, locationTwo, locationThree, locationFour]);

    // Update preview when roles or location changes
    useEffect(() => {
        updatePreview();
    }, [updatePreview]);

    const resetUserAssignment = async (id) => {
        const response = await API.delete(USER_LOCATION_ROLE_WITH_ID_URL(id));
        if (response.status === 204) {
            cogoToast.info('User Assignment Reset Success!', { position: 'top-right' })
            setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        }
    }

    const getRoleNameById = (roleId) => {
        // Search each category in allRoles for the roleId
        let roleName = 'Unknown Role'; // Default if not found
        Object.values(allRoles).forEach(category => {
            const role = category.find(role => role.id === roleId);
            if (role) {
                roleName = role.name;
                return;
            }
        });
        return roleName;
    };
    return (
        <CardOverlay>
            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={userColumns}
                    data={data}
                    title="Master User Data"
                    style={tableStyle}
                    actions={tableActions}
                    options={tableOptions}
                    localization={localization}
                />
            </ThemeProvider>
            <Modal
                show={mdShow}
                size="lg"
                onHide={() => setMdShow(false)}
                className='extra-large assign-permissions-modal'
                backdrop="static"
                keyboard={false}
            >
                <Modal.Header>
                    <div className='w-100 d-flex align-items-center justify-content-between'>
                        <h4 className='mb-0'>
                            Assign Permissions to {selectedUserId.name}
                        </h4>
                        <Button variant="primary" onClick={(e) => resetUserAssignment(selectedUserId.id)}>
                            Reset Assignment
                        </Button>
                    </div>
                </Modal.Header>

                <Modal.Body>
                    <div className="modal-content-wrapper">
                        <div className='row'>
                            <div className='col-lg-7 permissions-form-section'>
                                <div className="location-filter-section mb-4">
                                    <MasterUserFilterLocation
                                        handleFilter={handleFilter}
                                        countryRoles={me?.validationRoles}
                                        disableAll={true}
                                        period={false}
                                    />
                                </div>

                                <div className="roles-section">
                                    {((selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && individualSelectedRole.roles) &&
                                        moduleStructure.map((module, index) => (
                                            <div key={index} className="module-group">
                                                <h5 className="module-title">{module.name}</h5>
                                                <div className="role-checkboxes">
                                                    {module.roles.slice()
                                                        .sort((a, b) => {
                                                            const nameA = a.name.toLowerCase();
                                                            const nameB = b.name.toLowerCase();
                                                            if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                                return -1;
                                                            }
                                                            if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                                return 1;
                                                            }
                                                            return 0;
                                                        })
                                                        .map((role, roleIndex) => (
                                                            <label key={roleIndex} className={`role-checkbox-label ${individualSelectedRole.disabledRoles.includes(role.id) ? 'opacity-med' : ''}`}>
                                                                <input
                                                                    type="checkbox"
                                                                    className="role-checkbox"
                                                                    value={role.id}
                                                                    checked={individualSelectedRole.roles.includes(role.id) || individualSelectedRole.disabledRoles.includes(role.id)}
                                                                    disabled={individualSelectedRole.disabledRoles.includes(role.id)}
                                                                    onChange={(e) => handleRoleChange(e, module.key)}
                                                                />
                                                                <span className="role-name">{role.name}</span>
                                                            </label>
                                                        ))
                                                    }
                                                </div>
                                            </div>
                                        ))
                                    }
                                </div>
                            </div>

                            <div className='col-lg-5 preview-section'>
                                <div className='preview-container h-100'>
                                    <h5 className="section-title mb-3">List of Assigned Roles</h5>
                                    <div className="preview-content">
                                        {/* Current Selection Preview */}
                                        {previewRoles.length > 0 && (
                                            <div className="current-selection mb-4">
                                                <h6 className="preview-subtitle">Current Selection:</h6>
                                                {previewRoles.map((preview, index) => (
                                                    <div key={index} className="preview-item">
                                                        <div className="location-path mb-2">
                                                            <i className="mdi mdi-map-marker text-primary me-2"></i>
                                                            <strong>{preview.locationPath}</strong>
                                                        </div>
                                                        <div className="roles-by-module">
                                                            {Object.entries(preview.rolesByModule).map(([module, roles]) => (
                                                                <div key={module} className="module-preview mb-3">
                                                                    <div className="module-preview-title">
                                                                        <i className="mdi mdi-folder-outline text-info me-2"></i>
                                                                        {module}
                                                                    </div>
                                                                    <ul className="role-list">
                                                                        {roles.map((role, roleIndex) => (
                                                                            <li key={roleIndex} className="role-item">
                                                                                <i className="mdi mdi-check-circle text-success me-2"></i>
                                                                                {role}
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        {/* Existing Assignments */}
                                        {allLocationRoles.length > 0 && (
                                            <div className="existing-assignments">
                                                <h6 className="preview-subtitle">Existing Assignments:</h6>
                                                {allLocationRoles.map((locationRole, index) => {
                                                    // Initialize an array to hold the names for each level
                                                    let locationNames = [];
                                                    const roleNames = locationRole.roles.map(roleId => getRoleNameById(roleId));

                                                    // Function to get name by ID or handle special 'all' cases
                                                    const getLocationName = (id, locationArray, allText) => {
                                                        if (id === "") return ""; // Return empty if ID is not set
                                                        if (id.endsWith("-all")) return allText; // Handle 'all' cases
                                                        const location = locationArray.find(location => location.id === id);
                                                        return location ? location.name : 'Unknown';
                                                    };

                                                    // Get names for each level
                                                    locationNames.push(getLocationName(locationRole.locationOneId, country, 'All Country'));
                                                    locationNames.push(getLocationName(locationRole.locationTwoId, locationTwo, 'All City'));
                                                    locationNames.push(getLocationName(locationRole.locationThreeId, locationThree, 'All Business Unit'));
                                                    locationNames.push(getLocationName(locationRole.locationFourId, locationFour, 'All Projects / DC'));

                                                    // Filter out empty or unknown locations before joining
                                                    locationNames = locationNames.filter(name => name && name !== 'Unknown');

                                                    return (
                                                        <React.Fragment key={index}>
                                                            <div className="preview-item mb-3">
                                                                <div className="location-path mb-2">
                                                                    <i className="mdi mdi-map-marker text-primary me-2"></i>
                                                                    <strong>{locationNames.join(' > ')}</strong>
                                                                </div>
                                                                <ul className="role-list">
                                                                    {roleNames.map((name, roleIndex) => (
                                                                        <li key={roleIndex} className="role-item">
                                                                            <i className="mdi mdi-check-circle text-success me-2"></i>
                                                                            {name}
                                                                        </li>
                                                                    ))}
                                                                </ul>
                                                            </div>
                                                        </React.Fragment>
                                                    );
                                                })}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </Modal.Body>

                <Modal.Footer>
                    <Button variant="light" onClick={handleAssignClose}>Cancel</Button>
                    <Button variant="primary" onClick={handleAssignSubmit}>Assign Roles</Button>
                </Modal.Footer>
            </Modal>


            <Modal
                show={userShow}
                onHide={() => setUserShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="user_name" >Name</label>
                            <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_category" >Email</label>
                            <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_description" >Temporary Password</label>
                            <Form.Control type="password" ref={uPassword} id="user_description" placeholder="Enter Password" />
                        </div>



                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setUserShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={createUserHandler}>Create</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>
        </CardOverlay>
    )
}


export default MasterUser;
